#include <opencv2/opencv.hpp>
#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>
#include <opencv2/calib3d.hpp>
#include <opencv2/imgcodecs.hpp>
#include <pcl/io/pcd_io.h>
#include <pcl/point_types.h>
#include <iostream>
#include <vector>
#include <limits>

cv::Scalar fakeColor(float value) 
{
    value = std::max(0.f, std::min(1.f, value));
    value *= 255.0f;
    int r, g, b;
    if (value < 64) 
    {
        r = 0; g = static_cast<int>(value * 4); b = 255;
    } 
    else if (value < 128) 
    {
        r = 0; g = 255; b = static_cast<int>(255 - (value - 64)*4);
    } 
    else if (value < 192) 
    {
        r = static_cast<int>((value - 128)*4); g = 255; b = 0;
    } 
    else 
    {
        r = 255; g = static_cast<int>(255 - (value - 192)*4); b = 0;
    }
    return cv::Scalar(b, g, r);
}

// 创建圆形掩膜来处理鱼眼图像的有效区域
cv::Mat CreateCircularMask(const cv::Size& img_size, const cv::Point2f& center, float radius) 
{
    cv::Mat mask = cv::Mat::zeros(img_size, CV_8UC1);
    cv::circle(mask, center, static_cast<int>(radius), cv::Scalar(255), -1);
    return mask;
}

// 保持原始尺寸的鱼眼去畸变
cv::Mat UndistortFisheyeKeepSize(
    const cv::Mat& raw_img,
    const cv::Mat& K,
    const cv::Mat& D,
    double balance,
    cv::Mat& newK)
{
    // 使用原始图像尺寸作为输出尺寸
    cv::Size output_size = raw_img.size();
    
    // 估算新的内参矩阵，但保持输出尺寸不变
    cv::fisheye::estimateNewCameraMatrixForUndistortRectify(
        K, D, raw_img.size(),
        cv::Mat::eye(3, 3, CV_64F),
        newK, balance, output_size, 1.0
    );

    cv::Mat map1, map2;
    cv::fisheye::initUndistortRectifyMap(
        K, D, cv::Mat::eye(3, 3, CV_64F), newK,
        output_size, CV_16SC2, map1, map2
    );

    cv::Mat undistorted;
    cv::remap(raw_img, undistorted, map1, map2, cv::INTER_LINEAR, cv::BORDER_CONSTANT);

    return undistorted;
}

// 估算鱼眼有效区域
cv::Mat EstimateFisheyeValidRegion(const cv::Mat& undistorted_img) 
{
    cv::Mat gray;
    if (undistorted_img.channels() == 3)
        cv::cvtColor(undistorted_img, gray, cv::COLOR_BGR2GRAY);
    else
        gray = undistorted_img;

    // 使用适中的阈值检测有效区域
    cv::Mat mask;
    cv::threshold(gray, mask, 15, 255, cv::THRESH_BINARY);
    
    // 形态学操作平滑边界
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(5, 5));
    cv::morphologyEx(mask, mask, cv::MORPH_CLOSE, kernel);
    cv::morphologyEx(mask, mask, cv::MORPH_OPEN, kernel);
    
    return mask;
}

// 验证投影精度
void ValidateProjection(const std::vector<cv::Point3f>& cam_points,
                       const std::vector<cv::Point2f>& image_points,
                       const cv::Size& img_size) 
{
    int valid_count = 0;
    int total_count = 0;
    
    for (size_t i = 0; i < image_points.size(); i++) 
    {
        cv::Point2f uv = image_points[i];
        total_count++;
        
        if (uv.x >= 0 && uv.x < img_size.width && 
            uv.y >= 0 && uv.y < img_size.height) 
        {
            valid_count++;
        }
    }
    
    std::cout << "投影验证: " << valid_count << "/" << total_count 
              << " (" << (100.0 * valid_count / total_count) << "%)" << std::endl;
}

int main() 
{
    std::string img_path = "/auto_pro/fisheye_process/test/1750068965.119576.jpg";
    std::string pcd_path = "/auto_pro/fisheye_process/test/1750068965.099962.pcd";

    cv::Mat K = (cv::Mat_<double>(3,3) << 1720.87, 0.0, 1933.41,
                                          0.0, 1719.45, 1032.40,
                                          0.0, 0.0, 1.0);
    cv::Mat D = (cv::Mat_<double>(4,1) << 0.093171, -0.081098, 0.054832, -0.020865);

    cv::Mat T_cam_lidar = (cv::Mat_<double>(4,4) << 
        0.0190, -0.9994, -0.0320, -0.0259,
        0.9997,  0.0195, -0.0123,  0.0061,
        0.0129, -0.0317,  0.9994,  0.1097,
        0,       0,       0,       1);
    cv::Mat T_lidar_cam = T_cam_lidar.inv();

    cv::Mat raw_img = cv::imread(img_path);
    if (raw_img.empty()) {
        std::cerr << "无法读取图像: " << img_path << std::endl;
        return -1;
    }

    std::cout << "原始图像尺寸: " << raw_img.size() << std::endl;
    
    // 确保是3840x2160
    if (raw_img.cols != 3840 || raw_img.rows != 2160) 
    {
        std::cout << "警告: 图像尺寸不是3840x2160" << std::endl;
    }

    // Step 1: 保持原始尺寸的去畸变
    cv::Mat newK;
    double balance = 0.3;  // 调整balance值，减少黑边但保持有效区域
    cv::Mat undistorted = UndistortFisheyeKeepSize(raw_img, K, D, balance, newK);

    std::cout << "去畸变后图像尺寸: " << undistorted.size() << std::endl;

    // Step 2: 估算有效区域掩膜（用于后续处理参考）
    cv::Mat valid_mask = EstimateFisheyeValidRegion(undistorted);

    // 计算有效区域的统计信息
    std::vector<std::vector<cv::Point>> contours;
    cv::findContours(valid_mask, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
    
    if (!contours.empty()) 
    {
        // 找到最大的轮廓
        double max_area = 0;
        int max_contour_idx = 0;
        for (size_t i = 0; i < contours.size(); ++i) 
        {
            double area = cv::contourArea(contours[i]);
            if (area > max_area) {
                max_area = area;
                max_contour_idx = i;
            }
        }
        
        cv::Rect valid_bbox = cv::boundingRect(contours[max_contour_idx]);
        std::cout << "有效区域边界框: " << valid_bbox << std::endl;
        
        // 可以选择性地将有效区域外的区域设为特定颜色
        cv::Mat display_img = undistorted.clone();
        display_img.setTo(cv::Scalar(50, 50, 50), ~valid_mask);  // 将无效区域设为深灰色
        cv::imwrite("undistorted_with_mask.jpg", display_img);
    }

    // 打印新的内参和FOV
    double fx = newK.at<double>(0,0);
    double fy = newK.at<double>(1,1);
    double cx = newK.at<double>(0,2);
    double cy = newK.at<double>(1,2);
    
    std::cout << "新内参矩阵: " << std::endl << newK << std::endl;
    
    double fov_x = 2 * atan2(undistorted.cols, 2 * fx) * 180.0 / CV_PI;
    double fov_y = 2 * atan2(undistorted.rows, 2 * fy) * 180.0 / CV_PI;
    std::cout << "新FOV: (" << fov_x << "°, " << fov_y << "°)" << std::endl;

    // Step 3: 读取点云
    pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>);
    if (pcl::io::loadPCDFile<pcl::PointXYZ>(pcd_path, *cloud) == -1) 
    {
        std::cerr << "无法读取点云: " << pcd_path << std::endl;
        return -1;
    }

    std::cout << "点云总数: " << cloud->points.size() << std::endl;

    // Step 4: 转换点云到相机坐标系
    std::vector<cv::Point3f> cam_points;
    float min_z = std::numeric_limits<float>::max();
    float max_z = std::numeric_limits<float>::lowest();

    for (const auto& pt : cloud->points) {
        cv::Mat pt_lidar = (cv::Mat_<double>(4,1) << pt.x, pt.y, pt.z, 1.0);
        cv::Mat pt_cam = T_lidar_cam * pt_lidar;
        double x = pt_cam.at<double>(0,0);
        double y = pt_cam.at<double>(1,0);
        double z = pt_cam.at<double>(2,0);
        
        // 过滤有效深度范围的点
        if (z > 0.5 && z < 100.0) 
        {
            cam_points.emplace_back(x, y, z);
            min_z = std::min(min_z, (float)z);
            max_z = std::max(max_z, (float)z);
        }
    }

    std::cout << "有效点云数: " << cam_points.size() << std::endl;
    std::cout << "深度范围: [" << min_z << ", " << max_z << "]" << std::endl;

    if (cam_points.empty()) 
    {
        std::cerr << "无有效点" << std::endl;
        return -1;
    }

    float range_z = std::max(max_z - min_z, 1.0f);

    // Step 5: 投影点云到图像（保持3840x2160尺寸）
    std::vector<cv::Point2f> image_points;
    cv::Mat rvec = cv::Mat::zeros(3,1,CV_64F);
    cv::Mat tvec = cv::Mat::zeros(3,1,CV_64F);
    cv::Mat D_zero = cv::Mat::zeros(4,1,CV_64F);

    // 使用标准投影（因为已经去畸变）
    cv::projectPoints(cam_points, rvec, tvec, newK, D_zero, image_points);

    // 验证投影结果
    ValidateProjection(cam_points, image_points, undistorted.size());

    // Step 6: 绘制投影结果到3840x2160的图像上
    cv::Mat proj_img = undistorted.clone();
    cv::Mat depth_map = cv::Mat::zeros(undistorted.size(), CV_32F);
    
    int projected = 0;
    int valid_projected = 0;  // 在有效区域内的投影点数
    
    for (size_t i = 0; i < image_points.size(); i++) 
    {
        cv::Point2f uv = image_points[i];
        int u = static_cast<int>(uv.x + 0.5f);
        int v = static_cast<int>(uv.y + 0.5f);
        
        if (u >= 0 && u < proj_img.cols && v >= 0 && v < proj_img.rows) 
        {
            projected++;
            
            // 检查是否在有效区域内
            bool in_valid_region = (contours.empty() || valid_mask.at<uchar>(v, u) > 0);
            
            if (in_valid_region) 
            {
                valid_projected++;
            }
            
            float Z = cam_points[i].z;
            float norm_z = (Z - min_z) / range_z;
            
            // 深度测试
            if (depth_map.at<float>(v, u) == 0 || Z < depth_map.at<float>(v, u)) 
            {
                depth_map.at<float>(v, u) = Z;
                
                // 根据是否在有效区域使用不同的绘制方式
                if (in_valid_region) 
                {
                    cv::circle(proj_img, cv::Point(u, v), 3, fakeColor(norm_z), -1);
                } else {
                    // 在无效区域的点用较小的点和不同颜色标记
                    cv::circle(proj_img, cv::Point(u, v), 1, cv::Scalar(128, 128, 128), -1);
                }
            }
        }
    }

    std::cout << "总投影点数: " << projected << std::endl;
    std::cout << "有效区域投影点数: " << valid_projected << std::endl;

    // Step 7: 保存结果（全部保持3840x2160尺寸）
    cv::imwrite("undistorted.jpg", undistorted);
    cv::imwrite("projected.jpg", proj_img);

    // 创建深度图可视化
    cv::Mat depth_vis;
    cv::normalize(depth_map, depth_vis, 0, 255, cv::NORM_MINMAX, CV_8U);
    cv::applyColorMap(depth_vis, depth_vis, cv::COLORMAP_JET);
    cv::imwrite("depth_map.jpg", depth_vis);

    // 创建有效区域掩膜的可视化
    cv::imwrite("valid_mask.jpg", valid_mask);

    std::cout << "处理完成，所有输出图像尺寸: " << undistorted.size() << std::endl;
    
    return 0;
}